// Animation Controller for Title Sequence
class TitleSequenceController {
    constructor() {
        this.particleSystem = null;
        this.glitchSystem = new GlitchFragmentSystem();
        this.dustEffect = new DustEffect();
        this.isSequenceComplete = false;
        this.sequenceStartTime = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startSequence();
    }

    setupEventListeners() {
        // Skip button
        const skipButton = document.getElementById('skip-intro');
        if (skipButton) {
            skipButton.addEventListener('click', () => this.skipSequence());
        }

        // Auto-skip after sequence completes
        setTimeout(() => {
            if (!this.isSequenceComplete) {
                this.completeSequence();
            }
        }, 3000); // 3 seconds total sequence
    }

    startSequence() {
        this.sequenceStartTime = Date.now();

        // Initialize particle system
        const particleContainer = document.querySelector('.particle-system');
        if (particleContainer) {
            this.particleSystem = new ParticleSystem(particleContainer);
        }

        // Schedule title animations
        this.scheduleTitleAnimations();
    }

    scheduleTitleAnimations() {
        // Diana Mrabet title appears immediately
        setTimeout(() => {
            this.animateDianaTitle();
        }, 200);

        // 3D Artist & Game Developer title appears at 1.5s with glitch effect
        setTimeout(() => {
            this.animate3DArtistTitle();
        }, 1500);

        // Glitch fragments at 2.5s (très rapide)
        setTimeout(() => {
            this.triggerGlitchFragments();
        }, 2500);

        // Complete sequence at 3s
        setTimeout(() => {
            this.completeSequence();
        }, 3000);
    }

    animateDianaTitle() {
        const dianaTitle = document.getElementById('diana-title');
        if (dianaTitle) {
            // Add additional medieval effects
            dianaTitle.style.animation = 'dianaAppear 2s ease-in-out forwards, dianaDisappear 1s ease-in-out 2s forwards';

            // Add text shadow animation
            this.animateTextShadow(dianaTitle, '#8B4513', 2000);
        }
    }

    triggerDustEffect() {
        const dianaTitle = document.getElementById('diana-title');
        if (dianaTitle) {
            this.dustEffect.createDustEffect(dianaTitle);
        }
    }

    animate3DArtistTitle() {
        const artistTitle = document.getElementById('artist-title');
        if (artistTitle) {
            // Start the glitch animation
            artistTitle.style.animation = 'artistGlitchAppear 3s ease-in-out forwards';

            // Add additional glitch effects
            this.addGlitchEffects(artistTitle);
        }
    }

    addGlitchEffects(element) {
        let glitchInterval = setInterval(() => {
            if (Math.random() < 0.4) {
                // Random glitch plus intense
                element.style.transform = `translateX(${(Math.random() - 0.5) * 15}px) skew(${(Math.random() - 0.5) * 8}deg) scale(${0.95 + Math.random() * 0.1})`;
                element.style.textShadow = `${Math.random() * 6}px 0 #ff0080, ${-Math.random() * 6}px 0 #00ff00, 0 0 ${10 + Math.random() * 20}px #00ffff`;
                element.style.filter = `brightness(${1 + Math.random() * 0.5}) contrast(${1 + Math.random() * 0.5})`;

                setTimeout(() => {
                    element.style.transform = '';
                    element.style.textShadow = '0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff';
                    element.style.filter = '';
                }, 30);
            }
        }, 80);

        // Stop glitch effects after 0.5 seconds
        setTimeout(() => {
            clearInterval(glitchInterval);
        }, 500);
    }

    triggerGlitchFragments() {
        const artistTitle = document.getElementById('artist-title');
        if (artistTitle) {
            this.glitchSystem.createFragments(artistTitle);
        }
    }

    animateTextShadow(element, color, duration) {
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const intensity = Math.sin(progress * Math.PI * 4) * 10 + 10;
            element.style.textShadow = `0 0 ${intensity}px ${color}, 2px 2px 4px rgba(0, 0, 0, 0.7)`;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    skipSequence() {
        this.completeSequence();
    }

    completeSequence() {
        if (this.isSequenceComplete) return;

        this.isSequenceComplete = true;

        // Clean up particle system
        if (this.particleSystem) {
            this.particleSystem.destroy();
        }

        // Clean up any remaining effects
        this.glitchSystem.cleanup();

        // Hide title sequence
        const titleSequence = document.getElementById('title-sequence');
        const portfolioContent = document.getElementById('portfolio-content');

        if (titleSequence && portfolioContent) {
            titleSequence.style.transition = 'opacity 1s ease-out';
            titleSequence.style.opacity = '0';

            setTimeout(() => {
                titleSequence.style.display = 'none';
                portfolioContent.classList.remove('hidden');
                portfolioContent.style.opacity = '0';
                portfolioContent.style.transition = 'opacity 1s ease-in';

                // Trigger portfolio fade in
                setTimeout(() => {
                    portfolioContent.style.opacity = '1';
                }, 50);
            }, 1000);
        }
    }
}

// Smooth scrolling for navigation
class SmoothScrollController {
    constructor() {
        this.init();
    }

    init() {
        // Handle navigation clicks
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    this.scrollToElement(targetElement);
                    this.updateActiveNav(link);
                }
            });
        });

        // Handle CTA buttons
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const href = button.getAttribute('href');
                if (href && href.startsWith('#')) {
                    e.preventDefault();
                    const targetElement = document.querySelector(href);
                    if (targetElement) {
                        this.scrollToElement(targetElement);
                    }
                }
            });
        });

        // Update active nav on scroll
        window.addEventListener('scroll', () => {
            this.updateActiveNavOnScroll();
        });
    }

    scrollToElement(element) {
        const offsetTop = element.offsetTop - 80; // Account for fixed nav
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }

    updateActiveNav(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }

    updateActiveNavOnScroll() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.scrollY + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });

                const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }
            }
        });
    }
}

// Intersection Observer for animations
class ScrollAnimationController {
    constructor() {
        this.init();
    }

    init() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.project-card, .skill-category, .section-title').forEach(el => {
            this.observer.observe(el);
        });
    }
}

// Export controllers
window.TitleSequenceController = TitleSequenceController;
window.SmoothScrollController = SmoothScrollController;
window.ScrollAnimationController = ScrollAnimationController;
