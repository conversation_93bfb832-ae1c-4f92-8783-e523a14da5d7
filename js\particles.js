// Particle System for Spectral Effects
class ParticleSystem {
    constructor(container) {
        this.container = container;
        this.particles = [];
        this.maxParticles = 50;
        this.init();
    }

    init() {
        this.createParticles();
        this.animate();
    }

    createParticles() {
        for (let i = 0; i < this.maxParticles; i++) {
            this.createParticle();
        }
    }

    createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random position
        const x = Math.random() * window.innerWidth;
        const y = Math.random() * window.innerHeight;

        // Random size
        const size = Math.random() * 3 + 1;

        // Random opacity
        const opacity = Math.random() * 0.5 + 0.1;

        // Random color (spectral)
        const colors = ['#00ffff', '#ff0080', '#ffffff', '#8080ff'];
        const color = colors[Math.floor(Math.random() * colors.length)];

        particle.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: ${size}px;
            height: ${size}px;
            background: ${color};
            border-radius: 50%;
            opacity: ${opacity};
            pointer-events: none;
            box-shadow: 0 0 ${size * 2}px ${color};
            z-index: 1;
        `;

        // Add movement properties
        particle.vx = (Math.random() - 0.5) * 0.5;
        particle.vy = (Math.random() - 0.5) * 0.5;
        particle.life = Math.random() * 100 + 50;
        particle.maxLife = particle.life;

        this.container.appendChild(particle);
        this.particles.push(particle);
    }

    animate() {
        this.particles.forEach((particle, index) => {
            // Update position
            const currentX = parseFloat(particle.style.left);
            const currentY = parseFloat(particle.style.top);

            particle.style.left = (currentX + particle.vx) + 'px';
            particle.style.top = (currentY + particle.vy) + 'px';

            // Update life
            particle.life--;
            const lifeRatio = particle.life / particle.maxLife;
            particle.style.opacity = lifeRatio * 0.5;

            // Remove dead particles
            if (particle.life <= 0) {
                particle.remove();
                this.particles.splice(index, 1);
                this.createParticle(); // Create new particle
            }

            // Wrap around screen
            if (parseFloat(particle.style.left) > window.innerWidth) {
                particle.style.left = '0px';
            }
            if (parseFloat(particle.style.left) < 0) {
                particle.style.left = window.innerWidth + 'px';
            }
            if (parseFloat(particle.style.top) > window.innerHeight) {
                particle.style.top = '0px';
            }
            if (parseFloat(particle.style.top) < 0) {
                particle.style.top = window.innerHeight + 'px';
            }
        });

        requestAnimationFrame(() => this.animate());
    }

    destroy() {
        this.particles.forEach(particle => particle.remove());
        this.particles = [];
    }
}



// Dust Effect for Diana Mrabet title
class DustEffect {
    constructor() {
        this.dustParticles = [];
    }

    createDustEffect(element) {
        const rect = element.getBoundingClientRect();
        const particleCount = 30;

        for (let i = 0; i < particleCount; i++) {
            const dust = document.createElement('div');
            dust.className = 'dust-particle';

            const x = rect.left + Math.random() * rect.width;
            const y = rect.top + Math.random() * rect.height;
            const size = Math.random() * 2 + 1;

            dust.style.cssText = `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                width: ${size}px;
                height: ${size}px;
                background: #8B4513;
                border-radius: 50%;
                opacity: 0.7;
                pointer-events: none;
                z-index: 10000;
                box-shadow: 0 0 ${size}px #8B4513;
            `;

            dust.vx = (Math.random() - 0.5) * 2;
            dust.vy = Math.random() * -2 - 1;
            dust.life = 60;

            document.body.appendChild(dust);
            this.dustParticles.push(dust);
        }

        this.animateDust();
    }

    animateDust() {
        const animate = () => {
            this.dustParticles.forEach((dust, index) => {
                const currentX = parseFloat(dust.style.left);
                const currentY = parseFloat(dust.style.top);

                dust.style.left = (currentX + dust.vx) + 'px';
                dust.style.top = (currentY + dust.vy) + 'px';

                dust.life--;
                dust.style.opacity = dust.life / 60 * 0.7;

                if (dust.life <= 0) {
                    dust.remove();
                    this.dustParticles.splice(index, 1);
                }
            });

            if (this.dustParticles.length > 0) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }
}

// Export for use in other files
window.ParticleSystem = ParticleSystem;
window.DustEffect = DustEffect;
