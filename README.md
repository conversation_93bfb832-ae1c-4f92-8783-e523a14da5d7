# Interactive Portfolio - <PERSON>

A cinematic and immersive portfolio for **<PERSON>**, 3D Artist and Game Developer, inspired by dark and enigmatic movie title sequences.

## 🎬 Features

### Cinematic Opening Sequence
- **Background transition**: White to deep black with spectral smoke effects
- **"<PERSON>abet" title**: Medieval typography with dust disappearing effect
- **"3D Artist" title**: Glitch effect with flying crystalline 3D fragments
- **Spectral lighting**: Lunar halo and dramatic shadows
- **Particle system**: Variable density smoke with scintillating particles

### Interactive Portfolio
- **Smooth navigation** with soft scrolling
- **Organized sections**: 3D Art, Game Development, About, Contact
- **Visual effects**: Scroll animations, hover effects
- **Responsive design**: Optimized for all screens

## 🛠️ Technologies Used

- **HTML5**: Semantic structure
- **CSS3**: Advanced animations, gradients, visual effects
- **JavaScript ES6+**: Particle system, animation controllers
- **Google Fonts**: <PERSON><PERSON><PERSON> (medieval), Orbitron (futuristic)

## 📁 Project Structure

```
MrabetDiana2025Porto/
├── index.html              # Page principale
├── css/
│   └── style.css           # Main styles
├── js/
│   ├── particles.js        # Particle system and effects
│   ├── animations.js       # Animation controllers
│   └── main.js            # Main application
└── README.md              # Documentation
```

## 🚀 Installation and Usage

### Method 1: Local Opening
1. Clone or download the project
2. Open `index.html` in your browser

### Method 2: Local Server (Recommended)
```bash
# With Python 3
python -m http.server 8000

# With Node.js (if http-server is installed)
npx http-server

# With PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## 🎨 Personnalisation

### Couleurs Principales
- **Cyan** : `#00ffff` (éléments principaux)
- **Magenta** : `#ff0080` (accents)
- **Brun médiéval** : `#8B4513` (titre Diana)
- **Noir profond** : `#000000` (arrière-plan)

### Polices
- **Cinzel** : Titres médiévaux
- **Orbitron** : Texte futuriste/tech

### Animations
- **Durée totale** : 8-10 secondes
- **Transition de fond** : 8 secondes
- **Diana Mrabet** : Apparition 1s, disparition 3s
- **3D Artist** : Apparition 4s, fragments 7s

## 🌐 Déploiement GitHub Pages

1. **Créer un repository GitHub**
2. **Uploader les fichiers**
3. **Activer GitHub Pages** :
   - Aller dans Settings > Pages
   - Source : Deploy from a branch
   - Branch : main / root
4. **Accéder au site** : `https://username.github.io/repository-name`

## 📱 Compatibilité

- **Navigateurs modernes** : Chrome, Firefox, Safari, Edge
- **Responsive** : Mobile, tablette, desktop
- **Performance** : Optimisé pour 60fps

## 🎯 Fonctionnalités Avancées

### Système de Particules
- Particules spectrales flottantes
- Fragments 3D cristallins
- Effet de poussière médiévale

### Animations Interactives
- Glitch effects en temps réel
- Parallax subtil
- Animations au scroll
- Effets de survol dynamiques

### Navigation Intelligente
- Défilement fluide
- Indicateur de section active
- Bouton "Skip Intro"
- Navigation responsive

## 🔧 Développement

### Ajout de Projets
Modifiez les sections `.project-card` dans `index.html` :

```html
<div class="project-card">
    <div class="project-image">
        <img src="path/to/image.jpg" alt="Projet">
    </div>
    <div class="project-info">
        <h4 class="project-title">Nom du Projet</h4>
        <p class="project-description">Description</p>
    </div>
</div>
```

### Personnalisation des Animations
Modifiez les timings dans `js/animations.js` :

```javascript
// Exemple : changer la durée d'apparition
setTimeout(() => {
    this.animateDianaTitle();
}, 1000); // Délai en millisecondes
```

## 📄 Licence

Ce projet est sous licence MIT. Libre d'utilisation et de modification.

## 👤 Contact

**Diana Mrabet**
- Email : <EMAIL>
- LinkedIn : [diana-mrabet](https://linkedin.com/in/diana-mrabet)
- GitHub : [diana-mrabet](https://github.com/diana-mrabet)

---

*Portfolio créé avec passion pour l'art 3D et le développement de jeux* ✨
