# Portfolio Interactif - <PERSON>t

Un portfolio cinématique et immersif pour **Diana <PERSON>**, 3D Artist et Game Developer, inspiré des génériques de films sombres et énigmatiques.

## 🎬 Caractéristiques

### Séquence d'Ouverture Cinématique
- **Transition de fond** : Blanc vers noir profond avec effets de fumée spectrale
- **Titre "Diana Mrabet"** : Typographie médiévale avec effet de disparition en poussière
- **Titre "3D Artist"** : Effet glitch avec fragments 3D cristallins volants
- **Éclairage spectral** : Halo lunaire et ombres dramatiques
- **Système de particules** : Fumée à densité variable avec particules scintillantes

### Portfolio Interactif
- **Navigation fluide** avec défilement doux
- **Sections organisées** : Art 3D, Game Development, À propos, Contact
- **Effets visuels** : Animations au scroll, effets de survol
- **Design responsive** : Optimisé pour tous les écrans

## 🛠️ Technologies Utilisées

- **HTML5** : Structure sémantique
- **CSS3** : Animations avancées, gradients, effets visuels
- **JavaScript ES6+** : Système de particules, contrôleurs d'animation
- **Google Fonts** : Cinzel (médiéval), Orbitron (futuriste)

## 📁 Structure du Projet

```
MrabetDiana2025Porto/
├── index.html              # Page principale
├── css/
│   └── style.css           # Styles principaux
├── js/
│   ├── particles.js        # Système de particules et effets
│   ├── animations.js       # Contrôleurs d'animation
│   └── main.js            # Application principale
└── README.md              # Documentation
```

## 🚀 Installation et Utilisation

### Méthode 1: Ouverture Locale
1. Clonez ou téléchargez le projet
2. Ouvrez `index.html` dans votre navigateur

### Méthode 2: Serveur Local (Recommandé)
```bash
# Avec Python 3
python -m http.server 8000

# Avec Node.js (si http-server est installé)
npx http-server

# Avec PHP
php -S localhost:8000
```

Puis ouvrez `http://localhost:8000` dans votre navigateur.

## 🎨 Personnalisation

### Couleurs Principales
- **Cyan** : `#00ffff` (éléments principaux)
- **Magenta** : `#ff0080` (accents)
- **Brun médiéval** : `#8B4513` (titre Diana)
- **Noir profond** : `#000000` (arrière-plan)

### Polices
- **Cinzel** : Titres médiévaux
- **Orbitron** : Texte futuriste/tech

### Animations
- **Durée totale** : 8-10 secondes
- **Transition de fond** : 8 secondes
- **Diana Mrabet** : Apparition 1s, disparition 3s
- **3D Artist** : Apparition 4s, fragments 7s

## 🌐 Déploiement GitHub Pages

1. **Créer un repository GitHub**
2. **Uploader les fichiers**
3. **Activer GitHub Pages** :
   - Aller dans Settings > Pages
   - Source : Deploy from a branch
   - Branch : main / root
4. **Accéder au site** : `https://username.github.io/repository-name`

## 📱 Compatibilité

- **Navigateurs modernes** : Chrome, Firefox, Safari, Edge
- **Responsive** : Mobile, tablette, desktop
- **Performance** : Optimisé pour 60fps

## 🎯 Fonctionnalités Avancées

### Système de Particules
- Particules spectrales flottantes
- Fragments 3D cristallins
- Effet de poussière médiévale

### Animations Interactives
- Glitch effects en temps réel
- Parallax subtil
- Animations au scroll
- Effets de survol dynamiques

### Navigation Intelligente
- Défilement fluide
- Indicateur de section active
- Bouton "Skip Intro"
- Navigation responsive

## 🔧 Développement

### Ajout de Projets
Modifiez les sections `.project-card` dans `index.html` :

```html
<div class="project-card">
    <div class="project-image">
        <img src="path/to/image.jpg" alt="Projet">
    </div>
    <div class="project-info">
        <h4 class="project-title">Nom du Projet</h4>
        <p class="project-description">Description</p>
    </div>
</div>
```

### Personnalisation des Animations
Modifiez les timings dans `js/animations.js` :

```javascript
// Exemple : changer la durée d'apparition
setTimeout(() => {
    this.animateDianaTitle();
}, 1000); // Délai en millisecondes
```

## 📄 Licence

Ce projet est sous licence MIT. Libre d'utilisation et de modification.

## 👤 Contact

**Diana Mrabet**
- Email : <EMAIL>
- LinkedIn : [diana-mrabet](https://linkedin.com/in/diana-mrabet)
- GitHub : [diana-mrabet](https://github.com/diana-mrabet)

---

*Portfolio créé avec passion pour l'art 3D et le développement de jeux* ✨
