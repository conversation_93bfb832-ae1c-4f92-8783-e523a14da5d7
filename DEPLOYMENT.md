# Guide de Déploiement - <PERSON><PERSON><PERSON> Mr<PERSON>t

Ce guide vous explique comment déployer le portfolio sur GitHub Pages et d'autres plateformes.

## 🚀 Déploiement sur GitHub Pages

### Méthode Automatique (Recommandée)

1. **Créer un repository GitHub**
   ```bash
   # Initialiser git dans le dossier du projet
   git init
   git add .
   git commit -m "Initial commit: Portfolio Diana Mrabet"
   
   # Ajouter le remote GitHub
   git remote add origin https://github.com/VOTRE-USERNAME/VOTRE-REPO.git
   git branch -M main
   git push -u origin main
   ```

2. **Activer GitHub Pages**
   - Aller dans les **Settings** du repository
   - Scroller jusqu'à **Pages**
   - Source : **GitHub Actions**
   - Le workflow `.github/workflows/deploy.yml` se déclenchera automatiquement

3. **Accéder au site**
   - URL : `https://VOTRE-USERNAME.github.io/VOTRE-REPO/`
   - Le déploiement prend 2-5 minutes

### Méthode Manuelle

1. **Activer GitHub Pages**
   - Settings > Pages
   - Source : **Deploy from a branch**
   - Branch : **main** / **root**

2. **Le site sera disponible à** : `https://VOTRE-USERNAME.github.io/VOTRE-REPO/`

## 🌐 Autres Plateformes de Déploiement

### Netlify

1. **Drag & Drop**
   - Aller sur [netlify.com](https://netlify.com)
   - Glisser-déposer le dossier du projet
   - Site déployé instantanément

2. **Git Integration**
   - Connecter le repository GitHub
   - Déploiement automatique à chaque push

### Vercel

1. **Import Project**
   - Aller sur [vercel.com](https://vercel.com)
   - Import Git Repository
   - Sélectionner le repository

2. **Configuration**
   - Framework Preset : **Other**
   - Build Command : (laisser vide)
   - Output Directory : (laisser vide)

### GitHub Codespaces

1. **Ouvrir dans Codespaces**
   - Bouton "Code" > "Codespaces" > "Create codespace"
   - Le portfolio s'ouvre dans un environnement cloud

2. **Prévisualiser**
   - Terminal : `python -m http.server 8000`
   - Ouvrir le port 8000 dans l'onglet "Ports"

## 🔧 Configuration Avancée

### Domaine Personnalisé

1. **Acheter un domaine** (ex: diana-mrabet.com)

2. **Configuration DNS**
   ```
   Type: CNAME
   Name: www
   Value: VOTRE-USERNAME.github.io
   
   Type: A
   Name: @
   Value: 185.199.108.153
   Value: 185.199.109.153
   Value: 185.199.110.153
   Value: 185.199.111.153
   ```

3. **GitHub Pages Settings**
   - Custom domain : `www.diana-mrabet.com`
   - Enforce HTTPS : ✅

### Optimisations SEO

1. **Ajouter un sitemap.xml**
   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
     <url>
       <loc>https://VOTRE-DOMAINE.com/</loc>
       <lastmod>2025-01-01</lastmod>
       <priority>1.0</priority>
     </url>
   </urlset>
   ```

2. **Ajouter robots.txt**
   ```
   User-agent: *
   Allow: /
   
   Sitemap: https://VOTRE-DOMAINE.com/sitemap.xml
   ```

3. **Meta tags dans index.html**
   ```html
   <meta property="og:title" content="Diana Mrabet - 3D Artist & Game Developer">
   <meta property="og:description" content="Portfolio interactif de Diana Mrabet">
   <meta property="og:image" content="https://VOTRE-DOMAINE.com/preview.jpg">
   <meta property="og:url" content="https://VOTRE-DOMAINE.com">
   <meta name="twitter:card" content="summary_large_image">
   ```

## 📊 Analytics et Monitoring

### Google Analytics

1. **Créer un compte** sur [analytics.google.com](https://analytics.google.com)

2. **Ajouter le code de suivi** dans `index.html` :
   ```html
   <!-- Google Analytics -->
   <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
   <script>
     window.dataLayer = window.dataLayer || [];
     function gtag(){dataLayer.push(arguments);}
     gtag('js', new Date());
     gtag('config', 'GA_MEASUREMENT_ID');
   </script>
   ```

### Performance Monitoring

1. **Lighthouse** (intégré dans Chrome DevTools)
2. **PageSpeed Insights** : [pagespeed.web.dev](https://pagespeed.web.dev)
3. **GTmetrix** : [gtmetrix.com](https://gtmetrix.com)

## 🔄 Mise à Jour

### Workflow de Développement

1. **Développement local**
   ```bash
   # Créer une branche
   git checkout -b feature/nouvelle-fonctionnalite
   
   # Faire les modifications
   # Tester localement
   
   # Commit et push
   git add .
   git commit -m "Ajout nouvelle fonctionnalité"
   git push origin feature/nouvelle-fonctionnalite
   ```

2. **Déploiement**
   ```bash
   # Merger dans main
   git checkout main
   git merge feature/nouvelle-fonctionnalite
   git push origin main
   
   # Le déploiement GitHub Actions se déclenche automatiquement
   ```

## 🛠️ Dépannage

### Problèmes Courants

1. **Site non accessible**
   - Vérifier que GitHub Pages est activé
   - Attendre 5-10 minutes après le premier déploiement

2. **Animations ne fonctionnent pas**
   - Vérifier la console du navigateur (F12)
   - S'assurer que JavaScript est activé

3. **Polices ne se chargent pas**
   - Vérifier la connexion internet
   - Les polices Google Fonts nécessitent une connexion

4. **Performance lente**
   - Optimiser les images
   - Réduire le nombre de particules
   - Utiliser `prefers-reduced-motion`

### Support

- **Issues GitHub** : Créer une issue sur le repository
- **Documentation** : Consulter le README.md
- **Community** : Forums GitHub, Stack Overflow

---

*Guide créé pour le portfolio de Diana Mrabet* 🚀
