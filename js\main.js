// Main Application Controller
class PortfolioApp {
    constructor() {
        this.titleSequenceController = null;
        this.smoothScrollController = null;
        this.scrollAnimationController = null;
        this.isLoaded = false;

        this.init();
    }

    init() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    onDOMReady() {
        console.log('Portfolio app initializing...');

        // Initialize title sequence
        this.titleSequenceController = new TitleSequenceController();

        // Initialize other controllers after a delay to ensure title sequence starts first
        setTimeout(() => {
            this.smoothScrollController = new SmoothScrollController();
            this.scrollAnimationController = new ScrollAnimationController();
        }, 1000);

        // Add additional interactive features
        this.setupInteractiveFeatures();

        // Mark as loaded
        this.isLoaded = true;
        console.log('Portfolio app loaded successfully');
    }

    setupInteractiveFeatures() {
        // Add hover effects to project cards
        this.setupProjectCardEffects();

        // Add typing effect to hero description
        this.setupTypingEffect();

        // Add parallax effect to background
        this.setupParallaxEffect();

        // Add responsive navigation
        this.setupResponsiveNav();
    }

    setupProjectCardEffects() {
        document.querySelectorAll('.project-card').forEach(card => {
            card.addEventListener('mouseenter', (e) => {
                this.addCardHoverEffect(e.target);
            });

            card.addEventListener('mouseleave', (e) => {
                this.removeCardHoverEffect(e.target);
            });
        });
    }

    addCardHoverEffect(card) {
        const image = card.querySelector('.project-image');
        if (image) {
            image.style.transform = 'scale(1.05)';
            image.style.transition = 'transform 0.3s ease';
        }

        // Add subtle glow effect
        card.style.boxShadow = '0 20px 40px rgba(0, 255, 255, 0.3), 0 0 20px rgba(0, 255, 255, 0.1)';
    }

    removeCardHoverEffect(card) {
        const image = card.querySelector('.project-image');
        if (image) {
            image.style.transform = 'scale(1)';
        }

        card.style.boxShadow = '';
    }

    setupTypingEffect() {
        const heroDescription = document.querySelector('.hero-description');
        if (heroDescription) {
            const text = "Creator of immersive visual worlds and captivating interactive experiences";
            heroDescription.textContent = '';

            // Start typing effect after title sequence
            setTimeout(() => {
                this.typeText(heroDescription, text, 50);
            }, 9000); // Start after title sequence
        }
    }

    typeText(element, text, speed) {
        let i = 0;
        const timer = setInterval(() => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    setupParallaxEffect() {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.hero-section');

            parallaxElements.forEach(element => {
                const speed = 0.5;
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });
    }

    setupResponsiveNav() {
        // Mobile menu toggle (if needed in future)
        const navContainer = document.querySelector('.nav-container');

        // Add scroll effect to navigation
        window.addEventListener('scroll', () => {
            const nav = document.querySelector('.main-nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(0, 0, 0, 0.95)';
                nav.style.backdropFilter = 'blur(15px)';
            } else {
                nav.style.background = 'rgba(0, 0, 0, 0.9)';
                nav.style.backdropFilter = 'blur(10px)';
            }
        });
    }
}

// Utility functions
const Utils = {
    // Debounce function for performance
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function for scroll events
    throttle(func, limit) {
        let inThrottle;
        return function () {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Check if element is in viewport
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },

    // Smooth scroll to element
    smoothScrollTo(element, offset = 0) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }
};

// Performance monitoring
const Performance = {
    startTime: performance.now(),

    logLoadTime() {
        window.addEventListener('load', () => {
            const loadTime = performance.now() - this.startTime;
            console.log(`Portfolio loaded in ${loadTime.toFixed(2)}ms`);
        });
    },

    // Monitor frame rate for animations
    monitorFPS() {
        let lastTime = performance.now();
        let frames = 0;

        const checkFPS = (currentTime) => {
            frames++;
            if (currentTime >= lastTime + 1000) {
                const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                if (fps < 30) {
                    console.warn(`Low FPS detected: ${fps}`);
                }
                frames = 0;
                lastTime = currentTime;
            }
            requestAnimationFrame(checkFPS);
        };

        requestAnimationFrame(checkFPS);
    }
};

// Error handling
window.addEventListener('error', (e) => {
    console.error('Portfolio error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

// Initialize the application
const app = new PortfolioApp();

// Start performance monitoring
Performance.logLoadTime();
Performance.monitorFPS();

// Make utilities available globally
window.Utils = Utils;
window.PortfolioApp = app;

// Add CSS animation classes dynamically
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.6s ease-out forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .project-card {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .project-card.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .skill-category {
        opacity: 0;
        transform: translateX(-30px);
        transition: all 0.6s ease;
    }
    
    .skill-category.animate-in {
        opacity: 1;
        transform: translateX(0);
    }
`;
document.head.appendChild(style);
