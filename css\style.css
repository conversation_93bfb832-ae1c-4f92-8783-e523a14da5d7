/* ===== CSS RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Orbitron', sans-serif;
    background: #000;
    color: #fff;
    overflow-x: hidden;
    line-height: 1.6;
}

.hidden {
    display: none !important;
}

/* ===== TITLE SEQUENCE ===== */
.title-sequence {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #ffffff 0%, #f0f0f0 50%, #000000 100%);
    background-size: 400% 400%;
    animation: backgroundTransition 8s ease-in-out forwards;
}

@keyframes backgroundTransition {
    0% {
        background-position: 0% 0%;
        background: #ffffff;
    }

    30% {
        background: #f5f5f5;
    }

    60% {
        background: #333333;
    }

    100% {
        background-position: 100% 100%;
        background: #000000;
    }
}

/* Background Effects */
.background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.smoke-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 70%, rgba(100, 100, 100, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(150, 150, 150, 0.2) 0%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(80, 80, 80, 0.4) 0%, transparent 70%);
    animation: smokeFlow 12s ease-in-out infinite;
    opacity: 0;
    animation-delay: 2s;
}

@keyframes smokeFlow {

    0%,
    100% {
        opacity: 0;
        transform: scale(1) rotate(0deg);
    }

    25% {
        opacity: 0.6;
        transform: scale(1.1) rotate(2deg);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.2) rotate(-1deg);
    }

    75% {
        opacity: 0.4;
        transform: scale(1.05) rotate(1deg);
    }
}

.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.spectral-light {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(200, 200, 255, 0.3) 0%, rgba(150, 150, 255, 0.1) 40%, transparent 70%);
    border-radius: 50%;
    animation: spectralPulse 4s ease-in-out infinite;
    opacity: 0;
    animation-delay: 3s;
}

@keyframes spectralPulse {

    0%,
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.5);
    }
}

/* Title Container */
.title-container {
    position: relative;
    z-index: 10;
    text-align: center;
}

/* Diana Mrabet Title - Medieval Style */
.diana-title {
    font-family: 'Cinzel', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #8B4513;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    letter-spacing: 0.1em;
    opacity: 0;
    animation: dianaAppear 2s ease-in-out 1s forwards, dianaDisappear 1s ease-in-out 3s forwards;
    position: relative;
}

@keyframes dianaAppear {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.8);
        filter: blur(10px);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes dianaDisappear {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }

    100% {
        opacity: 0;
        transform: scale(0.9);
        filter: blur(5px);
    }
}

/* 3D Artist Title - Glitch Effect */
.artist-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 3rem;
    font-weight: 900;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff;
    letter-spacing: 0.2em;
    opacity: 0;
    animation: artistGlitchAppear 3s ease-in-out 4s forwards;
    position: relative;
    margin-top: 2rem;
}

@keyframes artistGlitchAppear {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }

    10% {
        opacity: 0.3;
        transform: translateX(20px) skew(5deg);
        color: #ff0080;
    }

    20% {
        opacity: 0.7;
        transform: translateX(-10px) skew(-3deg);
        color: #00ffff;
        text-shadow: 2px 0 #ff0080, -2px 0 #00ff00;
    }

    30% {
        opacity: 1;
        transform: translateX(5px) skew(1deg);
        color: #ffffff;
    }

    40% {
        transform: translateX(0) skew(0deg);
        color: #00ffff;
        text-shadow: 0 0 10px #00ffff;
    }

    60% {
        opacity: 0.8;
        transform: scale(1.1);
        filter: brightness(1.5);
    }

    80% {
        opacity: 0.5;
        transform: scale(0.95);
        filter: brightness(0.8);
    }

    100% {
        opacity: 0;
        transform: scale(0.8);
        filter: blur(3px);
    }
}

/* Skip Button */
.skip-button {
    position: absolute;
    bottom: 30px;
    right: 30px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 10px 20px;
    border-radius: 25px;
    font-family: 'Orbitron', sans-serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    opacity: 0;
    animation: skipButtonAppear 0.5s ease-in-out 1s forwards;
}

@keyframes skipButtonAppear {
    to {
        opacity: 1;
    }
}

.skip-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* ===== MAIN PORTFOLIO CONTENT ===== */
.portfolio-content {
    min-height: 100vh;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

/* Navigation */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo-text {
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #00ffff, #ff0080);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Sections */
section {
    padding: 5rem 0;
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Hero Section */
.hero-section {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    position: relative;
}

.hero-content {
    max-width: 800px;
    z-index: 2;
}

.hero-title {
    font-family: 'Cinzel', serif;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #00ffff, #ff0080, #00ff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #00ffff;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.hero-description {
    font-size: 1.1rem;
    color: #ccc;
    margin-bottom: 2.5rem;
    line-height: 1.8;
}

.hero-cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-button {
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button.primary {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: #000;
    border: none;
}

.cta-button.secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
}

/* Portfolio Section */
.portfolio-section {
    background: rgba(10, 10, 10, 0.8);
}

.portfolio-grid {
    display: grid;
    gap: 4rem;
}

.portfolio-category {
    margin-bottom: 3rem;
}

.category-title {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    color: #ff0080;
    margin-bottom: 2rem;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
}

.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.project-card {
    background: rgba(20, 20, 20, 0.8);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
}

.project-image {
    height: 200px;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.placeholder-image {
    color: #666;
    font-size: 1.2rem;
    text-align: center;
}

.project-info {
    padding: 1.5rem;
}

.project-title {
    font-size: 1.3rem;
    color: #00ffff;
    margin-bottom: 0.5rem;
}

.project-description {
    color: #ccc;
    line-height: 1.6;
}

/* About Section */
.about-section {
    background: rgba(15, 15, 15, 0.9);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text p {
    margin-bottom: 1.5rem;
    color: #ccc;
    line-height: 1.8;
    font-size: 1.1rem;
}

.skills-grid {
    display: grid;
    gap: 2rem;
}

.skill-category {
    background: rgba(20, 20, 20, 0.6);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.skill-category h4 {
    color: #00ffff;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.skill-category ul {
    list-style: none;
}

.skill-category li {
    color: #ccc;
    padding: 0.3rem 0;
    position: relative;
    padding-left: 1rem;
}

.skill-category li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #ff0080;
}

/* Contact Section */
.contact-section {
    background: rgba(5, 5, 5, 0.9);
    text-align: center;
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-info p {
    font-size: 1.2rem;
    color: #ccc;
    margin-bottom: 2rem;
}

.contact-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.contact-link {
    color: #00ffff;
    text-decoration: none;
    font-size: 1.1rem;
    padding: 0.8rem 1.5rem;
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 25px;
    transition: all 0.3s ease;
    display: inline-block;
    min-width: 250px;
}

.contact-link:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: #00ffff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .project-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .diana-title {
        font-size: 2.5rem;
    }

    .artist-title {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-section {
        padding: 2rem 0;
        height: auto;
        min-height: 100vh;
    }

    .hero-content {
        padding: 2rem 1rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .cta-button {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-links {
        gap: 0.8rem;
    }

    .contact-link {
        min-width: 200px;
        width: 100%;
        max-width: 300px;
    }

    section {
        padding: 3rem 0;
    }

    .container {
        padding: 0 1rem;
    }

    .project-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .skills-grid {
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .diana-title {
        font-size: 2rem;
    }

    .artist-title {
        font-size: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .category-title {
        font-size: 1.5rem;
    }

    .nav-menu {
        font-size: 0.9rem;
    }

    .skip-button {
        bottom: 20px;
        right: 20px;
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    .spectral-light {
        width: 150px;
        height: 150px;
    }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .title-sequence {
        animation: none;
        background: #000;
    }

    .diana-title,
    .artist-title {
        animation: none;
        opacity: 1;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .diana-title {
        color: #ffffff;
        text-shadow: 2px 2px 0 #000000;
    }

    .artist-title {
        color: #ffffff;
        text-shadow: 2px 2px 0 #000000;
    }

    .nav-link {
        border: 1px solid transparent;
    }

    .nav-link:hover,
    .nav-link.active {
        border-color: #ffffff;
    }
}

/* Print styles */
@media print {

    .title-sequence,
    .particle-system,
    .smoke-layer,
    .spectral-light {
        display: none !important;
    }

    .portfolio-content {
        display: block !important;
    }

    .main-nav {
        position: static;
        background: transparent;
    }

    body {
        background: white;
        color: black;
    }
}