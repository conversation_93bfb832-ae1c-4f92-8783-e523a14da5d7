/* ===== CSS RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Orbitron', sans-serif;
    background: #000;
    color: #fff;
    overflow-x: hidden;
    line-height: 1.6;
}

.hidden {
    display: none !important;
}

/* ===== TITLE SEQUENCE ===== */
.title-sequence {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle at center, #000000 0%, #1a0033 30%, #000000 70%);
    overflow: hidden;
    animation: introSequence 12s ease-in-out forwards;
}

@keyframes introSequence {
    0% {
        background: #ffffff;
        transform: scale(1);
    }

    10% {
        background: radial-gradient(circle at center, #ffffff 0%, #e0e0e0 50%, #cccccc 100%);
    }

    25% {
        background: radial-gradient(circle at center, #666666 0%, #333333 50%, #000000 100%);
    }

    40% {
        background: radial-gradient(circle at center, #1a0033 0%, #000000 30%, #000000 100%);
    }

    60% {
        background: radial-gradient(circle at center, #000000 0%, #1a0033 50%, #000000 100%);
        transform: scale(1.05);
    }

    80% {
        background: radial-gradient(circle at center, #000000 0%, #0a0a0a 30%, #000000 100%);
        transform: scale(1);
    }

    100% {
        background: #000000;
        transform: scale(1);
    }
}

/* Cinematic Effects */
.title-sequence::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.1) 50%, transparent 100%),
        linear-gradient(0deg, transparent 0%, rgba(255, 0, 128, 0.1) 50%, transparent 100%);
    animation: cinematicSweep 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes cinematicSweep {

    0%,
    100% {
        opacity: 0;
        transform: translateX(-100%);
    }

    25% {
        opacity: 0.7;
        transform: translateX(0%);
    }

    50% {
        opacity: 1;
        transform: translateX(50%);
    }

    75% {
        opacity: 0.7;
        transform: translateX(100%);
    }
}

/* Background Effects */
.background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.smoke-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.4) 0%, transparent 40%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(128, 0, 255, 0.5) 0%, transparent 60%),
        radial-gradient(circle at 30% 30%, rgba(0, 255, 128, 0.3) 0%, transparent 45%);
    animation: vibrantSmokeFlow 10s ease-in-out infinite;
    opacity: 0;
    animation-delay: 1s;
}

@keyframes vibrantSmokeFlow {

    0%,
    100% {
        opacity: 0;
        transform: scale(1) rotate(0deg);
        filter: hue-rotate(0deg) brightness(1);
    }

    15% {
        opacity: 0.8;
        transform: scale(1.3) rotate(5deg);
        filter: hue-rotate(60deg) brightness(1.2);
    }

    35% {
        opacity: 1;
        transform: scale(1.5) rotate(-3deg);
        filter: hue-rotate(120deg) brightness(1.5);
    }

    55% {
        opacity: 0.9;
        transform: scale(1.2) rotate(8deg);
        filter: hue-rotate(240deg) brightness(1.3);
    }

    75% {
        opacity: 0.7;
        transform: scale(1.4) rotate(-2deg);
        filter: hue-rotate(300deg) brightness(1.1);
    }
}

.particle-system {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.spectral-light {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    background:
        radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, rgba(255, 0, 128, 0.4) 30%, rgba(128, 0, 255, 0.3) 60%, transparent 80%),
        radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 50%;
    animation: vibrantSpectralPulse 3s ease-in-out infinite;
    opacity: 0;
    animation-delay: 2s;
    filter: blur(1px);
}

@keyframes vibrantSpectralPulse {

    0%,
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
        filter: blur(1px) hue-rotate(0deg) brightness(1);
    }

    25% {
        opacity: 0.9;
        transform: translate(-50%, -50%) scale(1.2) rotate(90deg);
        filter: blur(0px) hue-rotate(90deg) brightness(1.3);
    }

    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.8) rotate(180deg);
        filter: blur(2px) hue-rotate(180deg) brightness(1.6);
    }

    75% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.4) rotate(270deg);
        filter: blur(1px) hue-rotate(270deg) brightness(1.2);
    }
}

/* Cinematic Light Rays */
.title-sequence::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 1%, transparent 2%),
        linear-gradient(-45deg, transparent 0%, rgba(0, 255, 255, 0.1) 1%, transparent 2%),
        linear-gradient(135deg, transparent 0%, rgba(255, 0, 128, 0.1) 1%, transparent 2%);
    background-size: 200px 200px, 150px 150px, 180px 180px;
    animation: lightRays 6s linear infinite;
    pointer-events: none;
    opacity: 0.7;
}

@keyframes lightRays {
    0% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }

    50% {
        transform: rotate(180deg) scale(1.2);
        opacity: 0.8;
    }

    100% {
        transform: rotate(360deg) scale(1);
        opacity: 0.3;
    }
}

/* Sparkling Stars */
.background-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #fff, transparent),
        radial-gradient(2px 2px at 40px 70px, #00ffff, transparent),
        radial-gradient(1px 1px at 90px 40px, #ff0080, transparent),
        radial-gradient(1px 1px at 130px 80px, #fff, transparent),
        radial-gradient(2px 2px at 160px 30px, #00ff80, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 4s linear infinite;
    opacity: 0;
    animation-delay: 3s;
}

@keyframes sparkle {

    0%,
    100% {
        opacity: 0;
        transform: scale(1);
    }

    25% {
        opacity: 1;
        transform: scale(1.1);
    }

    50% {
        opacity: 0.7;
        transform: scale(0.9);
    }

    75% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* Title Container */
.title-container {
    position: relative;
    z-index: 10;
    text-align: center;
}

/* Diana Mrabet Title - Medieval Style */
.diana-title {
    font-family: 'Cinzel', serif;
    font-size: 4rem;
    font-weight: 700;
    color: #FFD700;
    text-shadow:
        0 0 10px rgba(255, 215, 0, 0.8),
        0 0 20px rgba(255, 215, 0, 0.6),
        0 0 30px rgba(255, 215, 0, 0.4),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    letter-spacing: 0.1em;
    opacity: 0;
    animation: vibrantDianaAppear 3s ease-in-out 1s forwards, vibrantDianaDisappear 2s ease-in-out 4s forwards;
    position: relative;
}

@keyframes vibrantDianaAppear {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.5) rotateX(90deg);
        filter: blur(20px) brightness(0.5);
        text-shadow: 0 0 50px rgba(255, 215, 0, 1);
    }

    30% {
        opacity: 0.7;
        transform: translateY(10px) scale(0.9) rotateX(30deg);
        filter: blur(5px) brightness(1.2);
        text-shadow:
            0 0 20px rgba(255, 215, 0, 1),
            0 0 40px rgba(255, 255, 255, 0.8);
    }

    70% {
        opacity: 1;
        transform: translateY(-5px) scale(1.1) rotateX(-10deg);
        filter: blur(0px) brightness(1.5);
    }

    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
        filter: blur(0) brightness(1);
        text-shadow:
            0 0 10px rgba(255, 215, 0, 0.8),
            0 0 20px rgba(255, 215, 0, 0.6),
            0 0 30px rgba(255, 215, 0, 0.4);
    }
}

@keyframes vibrantDianaDisappear {
    0% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        filter: brightness(1);
    }

    30% {
        opacity: 0.8;
        transform: scale(1.2) rotate(5deg);
        filter: brightness(1.3) hue-rotate(30deg);
    }

    60% {
        opacity: 0.5;
        transform: scale(0.8) rotate(-3deg);
        filter: brightness(0.7) hue-rotate(60deg) blur(3px);
    }

    100% {
        opacity: 0;
        transform: scale(0.3) rotate(10deg);
        filter: blur(10px) brightness(0.3);
    }
}

/* 3D Artist Title - Glitch Effect */
.artist-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 3rem;
    font-weight: 900;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff, 0 0 20px #00ffff, 0 0 30px #00ffff;
    letter-spacing: 0.2em;
    opacity: 0;
    animation: artistGlitchAppear 3s ease-in-out 4s forwards;
    position: relative;
    margin-top: 2rem;
}

@keyframes artistGlitchAppear {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }

    10% {
        opacity: 0.3;
        transform: translateX(20px) skew(5deg);
        color: #ff0080;
    }

    20% {
        opacity: 0.7;
        transform: translateX(-10px) skew(-3deg);
        color: #00ffff;
        text-shadow: 2px 0 #ff0080, -2px 0 #00ff00;
    }

    30% {
        opacity: 1;
        transform: translateX(5px) skew(1deg);
        color: #ffffff;
    }

    40% {
        transform: translateX(0) skew(0deg);
        color: #00ffff;
        text-shadow: 0 0 10px #00ffff;
    }

    60% {
        opacity: 0.8;
        transform: scale(1.1);
        filter: brightness(1.5);
    }

    80% {
        opacity: 0.5;
        transform: scale(0.95);
        filter: brightness(0.8);
    }

    100% {
        opacity: 0;
        transform: scale(0.8);
        filter: blur(3px);
    }
}

/* Skip Button */
.skip-button {
    position: absolute;
    bottom: 30px;
    right: 30px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 10px 20px;
    border-radius: 25px;
    font-family: 'Orbitron', sans-serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    opacity: 0;
    animation: skipButtonAppear 0.5s ease-in-out 1s forwards;
}

@keyframes skipButtonAppear {
    to {
        opacity: 1;
    }
}

.skip-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* ===== MAIN PORTFOLIO CONTENT ===== */
.portfolio-content {
    min-height: 100vh;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

/* Navigation */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    height: 80px;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo-text {
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #fff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #00ffff, #ff0080);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Sections */
section {
    padding: 5rem 0;
}

/* Specific section spacing adjustments */
#home {
    padding: 0;
    margin-bottom: 0;
}

#portfolio {
    padding: 8rem 0 5rem 0;
    margin-top: 0;
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Hero Section */
.hero-section {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: radial-gradient(circle at center, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
    position: relative;
    margin-bottom: 0;
    padding: 0;
}

.hero-content {
    max-width: 800px;
    z-index: 2;
}

.hero-title {
    font-family: 'Cinzel', serif;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #00ffff, #ff0080, #00ff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #00ffff;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.hero-description {
    font-size: 1.1rem;
    color: #ccc;
    margin-bottom: 2.5rem;
    line-height: 1.8;
}

.hero-cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-button {
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-button.primary {
    background: linear-gradient(45deg, #00ffff, #0080ff);
    color: #000;
    border: none;
}

.cta-button.secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
}

/* Portfolio Section */
.portfolio-section {
    background: rgba(10, 10, 10, 0.8);
    margin-top: 0;
    padding-top: 8rem;
    position: relative;
    z-index: 2;
}

.portfolio-grid {
    display: grid;
    gap: 4rem;
}

.portfolio-category {
    margin-bottom: 3rem;
}

.category-title {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    color: #ff0080;
    margin-bottom: 2rem;
    text-align: center;
    text-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
}

.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.project-card {
    background: rgba(20, 20, 20, 0.8);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
}

.project-image {
    height: 200px;
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.placeholder-image {
    color: #666;
    font-size: 1.2rem;
    text-align: center;
}

.project-info {
    padding: 1.5rem;
}

.project-title {
    font-size: 1.3rem;
    color: #00ffff;
    margin-bottom: 0.5rem;
}

.project-description {
    color: #ccc;
    line-height: 1.6;
}

/* About Section */
.about-section {
    background: rgba(15, 15, 15, 0.9);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text p {
    margin-bottom: 1.5rem;
    color: #ccc;
    line-height: 1.8;
    font-size: 1.1rem;
}

.skills-grid {
    display: grid;
    gap: 2rem;
}

.skill-category {
    background: rgba(20, 20, 20, 0.6);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.skill-category h4 {
    color: #00ffff;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.skill-category ul {
    list-style: none;
}

.skill-category li {
    color: #ccc;
    padding: 0.3rem 0;
    position: relative;
    padding-left: 1rem;
}

.skill-category li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #ff0080;
}

/* Contact Section */
.contact-section {
    background: rgba(5, 5, 5, 0.9);
    text-align: center;
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-info p {
    font-size: 1.2rem;
    color: #ccc;
    margin-bottom: 2rem;
}

.contact-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.contact-link {
    color: #00ffff;
    text-decoration: none;
    font-size: 1.1rem;
    padding: 0.8rem 1.5rem;
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 25px;
    transition: all 0.3s ease;
    display: inline-block;
    min-width: 250px;
}

.contact-link:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: #00ffff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .project-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .diana-title {
        font-size: 2.5rem;
    }

    .artist-title {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0 1rem;
    }

    .nav-menu {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-section {
        padding: 0;
        height: auto;
        min-height: 100vh;
        margin-bottom: 0;
    }

    .portfolio-section {
        margin-top: 0;
        padding-top: 5rem;
    }

    #portfolio {
        padding: 5rem 0;
    }

    .hero-content {
        padding: 2rem 1rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .cta-button {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-links {
        gap: 0.8rem;
    }

    .contact-link {
        min-width: 200px;
        width: 100%;
        max-width: 300px;
    }

    section {
        padding: 3rem 0;
    }

    .container {
        padding: 0 1rem;
    }

    .project-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .skills-grid {
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .diana-title {
        font-size: 2rem;
    }

    .artist-title {
        font-size: 1.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .category-title {
        font-size: 1.5rem;
    }

    .nav-menu {
        font-size: 0.9rem;
    }

    .skip-button {
        bottom: 20px;
        right: 20px;
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    .spectral-light {
        width: 150px;
        height: 150px;
    }

    .hero-section {
        min-height: 90vh;
    }

    .portfolio-section {
        padding-top: 4rem;
    }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .title-sequence {
        animation: none;
        background: #000;
    }

    .diana-title,
    .artist-title {
        animation: none;
        opacity: 1;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .diana-title {
        color: #ffffff;
        text-shadow: 2px 2px 0 #000000;
    }

    .artist-title {
        color: #ffffff;
        text-shadow: 2px 2px 0 #000000;
    }

    .nav-link {
        border: 1px solid transparent;
    }

    .nav-link:hover,
    .nav-link.active {
        border-color: #ffffff;
    }
}

/* Print styles */
@media print {

    .title-sequence,
    .particle-system,
    .smoke-layer,
    .spectral-light {
        display: none !important;
    }

    .portfolio-content {
        display: block !important;
    }

    .main-nav {
        position: static;
        background: transparent;
    }

    body {
        background: white;
        color: black;
    }
}